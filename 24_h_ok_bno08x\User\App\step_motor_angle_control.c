/**
 * @file    step_motor_angle_control.c
 * @brief   EMM_V5步进电机云台角度控制函数实现
 * <AUTHOR> Team
 * @date    2025-01-26
 */

#include "step_motor_angle_control.h"
#include "Emm_V5.h"
#include "step_motor_bsp.h"
#include "usart_app.h"

/* Private defines -----------------------------------------------------------*/

// EMM_V5步进电机参数配置
#define MOTOR_STEPS_PER_REV         200     // 步进电机每圈步数（1.8°/步）
#define MOTOR_MICROSTEPS           16      // 细分数（EMM_V5默认16细分）
#define MOTOR_GEAR_RATIO           1.0f    // 减速比（如果有减速器）

// 角度转换计算
#define TOTAL_STEPS_PER_REV        (MOTOR_STEPS_PER_REV * MOTOR_MICROSTEPS * MOTOR_GEAR_RATIO)
#define DEGREES_PER_STEP           (360.0f / TOTAL_STEPS_PER_REV)
#define STEPS_PER_DEGREE           (TOTAL_STEPS_PER_REV / 360.0f)

// 运动参数
#define DEFAULT_ANGLE_SPEED        30      // 默认角度运动速度 (RPM)
#define DEFAULT_ANGLE_ACCEL        10      // 默认角度运动加速度
#define MAX_ANGLE_SPEED            100     // 最大角度运动速度 (RPM)

// 安全限位
#define MAX_SINGLE_MOVE_ANGLE      180.0f  // 单次最大移动角度
#define ANGLE_TOLERANCE            0.1f    // 角度容差

/* Private variables ---------------------------------------------------------*/

// 云台当前角度状态（相对于零点位置）
static float current_x_angle = 0.0f;
static float current_y_angle = 0.0f;

// 云台角度限位
static float x_angle_min = -50.0f;  // 默认±50°限位
static float x_angle_max = 50.0f;
static float y_angle_min = -50.0f;
static float y_angle_max = 50.0f;

// 运动状态标志
static uint8_t x_axis_moving = 0;
static uint8_t y_axis_moving = 0;

/* Private function prototypes -----------------------------------------------*/
static uint32_t Angle_To_Pulses(float angle);
static float Pulses_To_Angle(uint32_t pulses);
static int8_t Check_Angle_Limits(float x_angle, float y_angle);
static int8_t Execute_Angle_Move(uint8_t axis, float angle, uint16_t speed, uint8_t accel, uint8_t relative);

/* Exported functions --------------------------------------------------------*/

/**
 * @brief 云台角度控制系统初始化
 * @note 必须在使用角度控制功能前调用
 * @retval 0: 成功, -1: 失败
 */
int8_t Step_Motor_Angle_Init(void)
{
    // 初始化基础步进电机系统
    Step_Motor_Init();
    
    // 重置当前角度为零点
    current_x_angle = 0.0f;
    current_y_angle = 0.0f;
    
    // 重置运动状态
    x_axis_moving = 0;
    y_axis_moving = 0;
    
    my_printf(&huart1, "云台角度控制系统初始化完成\r\n");
    my_printf(&huart1, "角度分辨率: %.3f°/脉冲\r\n", DEGREES_PER_STEP);
    my_printf(&huart1, "X轴角度范围: %.1f° ~ %.1f°\r\n", x_angle_min, x_angle_max);
    my_printf(&huart1, "Y轴角度范围: %.1f° ~ %.1f°\r\n", y_angle_min, y_angle_max);
    
    return 0;
}

/**
 * @brief 控制云台旋转到指定的绝对角度
 * @param x_angle: X轴目标角度 (度)，正值为顺时针，负值为逆时针
 * @param y_angle: Y轴目标角度 (度)，正值为顺时针，负值为逆时针  
 * @param speed: 运动速度 (RPM)，范围 1-100，0表示使用默认速度
 * @retval 0: 成功, -1: 参数错误, -2: 超出限位, -3: 轴正在运动
 * 
 * @example 使用示例：
 *   Step_Motor_Rotate_To_Angle(45.0f, -30.0f, 50);  // X轴转到45°，Y轴转到-30°，速度50RPM
 *   Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 0);      // 回到零点位置，使用默认速度
 */
int8_t Step_Motor_Rotate_To_Angle(float x_angle, float y_angle, uint16_t speed)
{
    // 参数检查
    if (speed > MAX_ANGLE_SPEED) {
        my_printf(&huart1, "错误：速度超出范围 (最大%d RPM)\r\n", MAX_ANGLE_SPEED);
        return -1;
    }
    
    // 使用默认速度
    if (speed == 0) {
        speed = DEFAULT_ANGLE_SPEED;
    }
    
    // 检查角度限位
    if (Check_Angle_Limits(x_angle, y_angle) != 0) {
        my_printf(&huart1, "错误：目标角度超出限位范围\r\n");
        return -2;
    }
    
    // 检查轴是否正在运动
    if (x_axis_moving || y_axis_moving) {
        my_printf(&huart1, "错误：云台正在运动中，请等待完成\r\n");
        return -3;
    }
    
    // 计算需要移动的角度
    float x_move_angle = x_angle - current_x_angle;
    float y_move_angle = y_angle - current_y_angle;
    
    my_printf(&huart1, "绝对角度控制：\r\n");
    my_printf(&huart1, "  当前位置: X=%.2f°, Y=%.2f°\r\n", current_x_angle, current_y_angle);
    my_printf(&huart1, "  目标位置: X=%.2f°, Y=%.2f°\r\n", x_angle, y_angle);
    my_printf(&huart1, "  移动角度: X=%.2f°, Y=%.2f°\r\n", x_move_angle, y_move_angle);
    my_printf(&huart1, "  运动速度: %d RPM\r\n", speed);
    
    // 执行X轴运动
    if (fabs(x_move_angle) > ANGLE_TOLERANCE) {
        if (Execute_Angle_Move(0, x_move_angle, speed, DEFAULT_ANGLE_ACCEL, 1) == 0) {
            current_x_angle = x_angle;  // 更新当前角度
            x_axis_moving = 1;
        }
    }
    
    // 执行Y轴运动
    if (fabs(y_move_angle) > ANGLE_TOLERANCE) {
        if (Execute_Angle_Move(1, y_move_angle, speed, DEFAULT_ANGLE_ACCEL, 1) == 0) {
            current_y_angle = y_angle;  // 更新当前角度
            y_axis_moving = 1;
        }
    }
    
    return 0;
}

/**
 * @brief 控制云台相对当前位置旋转指定角度
 * @param x_delta: X轴相对角度 (度)，正值为顺时针，负值为逆时针
 * @param y_delta: Y轴相对角度 (度)，正值为顺时针，负值为逆时针
 * @param speed: 运动速度 (RPM)，范围 1-100，0表示使用默认速度
 * @retval 0: 成功, -1: 参数错误, -2: 超出限位, -3: 轴正在运动
 * 
 * @example 使用示例：
 *   Step_Motor_Rotate_By_Angle(10.0f, -5.0f, 30);   // X轴顺时针转10°，Y轴逆时针转5°
 *   Step_Motor_Rotate_By_Angle(-90.0f, 0.0f, 0);    // X轴逆时针转90°，Y轴不动
 */
int8_t Step_Motor_Rotate_By_Angle(float x_delta, float y_delta, uint16_t speed)
{
    // 参数检查
    if (speed > MAX_ANGLE_SPEED) {
        my_printf(&huart1, "错误：速度超出范围 (最大%d RPM)\r\n", MAX_ANGLE_SPEED);
        return -1;
    }
    
    // 检查单次移动角度限制
    if (fabs(x_delta) > MAX_SINGLE_MOVE_ANGLE || fabs(y_delta) > MAX_SINGLE_MOVE_ANGLE) {
        my_printf(&huart1, "错误：单次移动角度过大 (最大%.1f°)\r\n", MAX_SINGLE_MOVE_ANGLE);
        return -1;
    }
    
    // 使用默认速度
    if (speed == 0) {
        speed = DEFAULT_ANGLE_SPEED;
    }
    
    // 计算目标角度
    float target_x = current_x_angle + x_delta;
    float target_y = current_y_angle + y_delta;
    
    // 检查目标角度是否超出限位
    if (Check_Angle_Limits(target_x, target_y) != 0) {
        my_printf(&huart1, "错误：目标角度超出限位范围\r\n");
        my_printf(&huart1, "  当前: X=%.2f°, Y=%.2f°\r\n", current_x_angle, current_y_angle);
        my_printf(&huart1, "  增量: X=%.2f°, Y=%.2f°\r\n", x_delta, y_delta);
        my_printf(&huart1, "  目标: X=%.2f°, Y=%.2f°\r\n", target_x, target_y);
        return -2;
    }
    
    // 检查轴是否正在运动
    if (x_axis_moving || y_axis_moving) {
        my_printf(&huart1, "错误：云台正在运动中，请等待完成\r\n");
        return -3;
    }
    
    my_printf(&huart1, "相对角度控制：\r\n");
    my_printf(&huart1, "  当前位置: X=%.2f°, Y=%.2f°\r\n", current_x_angle, current_y_angle);
    my_printf(&huart1, "  相对角度: X=%.2f°, Y=%.2f°\r\n", x_delta, y_delta);
    my_printf(&huart1, "  目标位置: X=%.2f°, Y=%.2f°\r\n", target_x, target_y);
    my_printf(&huart1, "  运动速度: %d RPM\r\n", speed);
    
    // 执行X轴运动
    if (fabs(x_delta) > ANGLE_TOLERANCE) {
        if (Execute_Angle_Move(0, x_delta, speed, DEFAULT_ANGLE_ACCEL, 1) == 0) {
            current_x_angle = target_x;  // 更新当前角度
            x_axis_moving = 1;
        }
    }
    
    // 执行Y轴运动
    if (fabs(y_delta) > ANGLE_TOLERANCE) {
        if (Execute_Angle_Move(1, y_delta, speed, DEFAULT_ANGLE_ACCEL, 1) == 0) {
            current_y_angle = target_y;  // 更新当前角度
            y_axis_moving = 1;
        }
    }
    
    return 0;
}

/**
 * @brief 设置云台角度限位范围
 * @param x_min: X轴最小角度 (度)
 * @param x_max: X轴最大角度 (度)
 * @param y_min: Y轴最小角度 (度)
 * @param y_max: Y轴最大角度 (度)
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Step_Motor_Set_Angle_Limits(float x_min, float x_max, float y_min, float y_max)
{
    // 参数检查
    if (x_min >= x_max || y_min >= y_max) {
        my_printf(&huart1, "错误：角度限位参数无效\r\n");
        return -1;
    }
    
    if (fabs(x_max - x_min) > 360.0f || fabs(y_max - y_min) > 360.0f) {
        my_printf(&huart1, "错误：角度范围过大 (最大360°)\r\n");
        return -1;
    }
    
    // 设置新的限位
    x_angle_min = x_min;
    x_angle_max = x_max;
    y_angle_min = y_min;
    y_angle_max = y_max;
    
    my_printf(&huart1, "角度限位已更新：\r\n");
    my_printf(&huart1, "  X轴: %.1f° ~ %.1f°\r\n", x_angle_min, x_angle_max);
    my_printf(&huart1, "  Y轴: %.1f° ~ %.1f°\r\n", y_angle_min, y_angle_max);
    
    return 0;
}

/**
 * @brief 获取云台当前角度
 * @param x_angle: 返回X轴当前角度的指针
 * @param y_angle: 返回Y轴当前角度的指针
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Step_Motor_Get_Current_Angle(float *x_angle, float *y_angle)
{
    if (x_angle == NULL || y_angle == NULL) {
        return -1;
    }
    
    *x_angle = current_x_angle;
    *y_angle = current_y_angle;
    
    return 0;
}

/**
 * @brief 重置云台角度零点（将当前位置设为零点）
 * @retval 0: 成功
 */
int8_t Step_Motor_Reset_Angle_Zero(void)
{
    // 发送位置清零指令给EMM_V5
    Emm_V5_Reset_CurPos_To_Zero(&MOTOR_X_UART, MOTOR_X_ADDR);
    Emm_V5_Reset_CurPos_To_Zero(&MOTOR_Y_UART, MOTOR_Y_ADDR);
    
    // 重置软件角度记录
    current_x_angle = 0.0f;
    current_y_angle = 0.0f;
    
    my_printf(&huart1, "云台角度零点已重置\r\n");
    
    return 0;
}

/**
 * @brief 检查云台是否正在运动
 * @retval 0: 静止, 1: 运动中
 */
uint8_t Step_Motor_Is_Moving(void)
{
    return (x_axis_moving || y_axis_moving);
}

/**
 * @brief 停止云台运动
 * @retval 0: 成功
 */
int8_t Step_Motor_Stop_Angle_Move(void)
{
    // 发送停止指令
    Step_Motor_Stop();
    
    // 重置运动状态
    x_axis_moving = 0;
    y_axis_moving = 0;
    
    my_printf(&huart1, "云台运动已停止\r\n");
    
    return 0;
}

/**
 * @brief 云台角度控制任务（需要周期性调用）
 * @note 建议在10-50ms周期内调用，用于更新运动状态
 */
void Step_Motor_Angle_Task(void)
{
    // 这里可以添加运动状态检测逻辑
    // 例如：查询EMM_V5运动状态，更新x_axis_moving和y_axis_moving标志
    
    // 简单的超时保护（实际项目中应该查询EMM_V5状态）
    static uint32_t last_move_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    if ((x_axis_moving || y_axis_moving) && (current_time - last_move_time > 5000)) {
        // 5秒超时，认为运动完成
        x_axis_moving = 0;
        y_axis_moving = 0;
        my_printf(&huart1, "云台运动超时，状态已重置\r\n");
    }
    
    if (x_axis_moving || y_axis_moving) {
        last_move_time = current_time;
    }
}

/* Private functions ---------------------------------------------------------*/

/**
 * @brief 将角度转换为脉冲数
 * @param angle: 角度值 (度)
 * @retval 脉冲数
 */
static uint32_t Angle_To_Pulses(float angle)
{
    float pulses_float = fabs(angle) * STEPS_PER_DEGREE;
    return (uint32_t)(pulses_float + 0.5f);  // 四舍五入
}

/**
 * @brief 将脉冲数转换为角度
 * @param pulses: 脉冲数
 * @retval 角度值 (度)
 */
static float Pulses_To_Angle(uint32_t pulses)
{
    return (float)pulses * DEGREES_PER_STEP;
}

/**
 * @brief 检查角度是否在限位范围内
 * @param x_angle: X轴角度
 * @param y_angle: Y轴角度
 * @retval 0: 在范围内, -1: 超出范围
 */
static int8_t Check_Angle_Limits(float x_angle, float y_angle)
{
    if (x_angle < x_angle_min || x_angle > x_angle_max ||
        y_angle < y_angle_min || y_angle > y_angle_max) {
        return -1;
    }
    return 0;
}

/**
 * @brief 执行角度运动
 * @param axis: 轴选择 (0=X轴, 1=Y轴)
 * @param angle: 角度值 (度)
 * @param speed: 速度 (RPM)
 * @param accel: 加速度
 * @param relative: 相对/绝对标志 (1=相对, 0=绝对)
 * @retval 0: 成功, -1: 失败
 */
static int8_t Execute_Angle_Move(uint8_t axis, float angle, uint16_t speed, uint8_t accel, uint8_t relative)
{
    uint8_t dir;
    uint32_t pulses;
    UART_HandleTypeDef *uart;
    uint8_t addr;
    
    // 确定方向
    dir = (angle >= 0.0f) ? 0 : 1;  // 0=顺时针, 1=逆时针
    
    // 转换为脉冲数
    pulses = Angle_To_Pulses(angle);
    
    // 选择对应的串口和地址
    if (axis == 0) {  // X轴
        uart = &MOTOR_X_UART;
        addr = MOTOR_X_ADDR;
    } else {  // Y轴
        uart = &MOTOR_Y_UART;
        addr = MOTOR_Y_ADDR;
    }
    
    // 发送位置控制指令
    Emm_V5_Pos_Control(uart, addr, dir, speed, accel, pulses, relative, MOTOR_SYNC_FLAG);
    
    my_printf(&huart1, "  %s轴运动: 角度=%.2f°, 脉冲=%lu, 方向=%s\r\n", 
              (axis == 0) ? "X" : "Y", 
              angle, 
              pulses, 
              (dir == 0) ? "顺时针" : "逆时针");
    
    return 0;
}
