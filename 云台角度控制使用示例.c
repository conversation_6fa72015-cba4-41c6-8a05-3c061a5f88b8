/**
 * @file    云台角度控制使用示例.c
 * @brief   EMM_V5云台角度控制函数使用示例
 * <AUTHOR> Team
 * @date    2025-01-26
 */

#include "step_motor_angle_control.h"
#include "main.h"

/**
 * @brief 云台角度控制使用示例
 * @note 这个函数展示了如何使用云台角度控制功能
 */
void Gimbal_Angle_Control_Example(void)
{
    int8_t result;
    float current_x, current_y;
    
    // 1. 初始化云台角度控制系统
    printf("=== 云台角度控制系统初始化 ===\r\n");
    result = Step_Motor_Angle_Init();
    if (result == 0) {
        printf("✅ 云台角度控制系统初始化成功\r\n");
    } else {
        printf("❌ 云台角度控制系统初始化失败\r\n");
        return;
    }
    
    // 等待系统稳定
    HAL_Delay(1000);
    
    // 2. 设置角度限位（可选，默认为±50°）
    printf("\n=== 设置角度限位 ===\r\n");
    result = Step_Motor_Set_Angle_Limits(-90.0f, 90.0f, -45.0f, 45.0f);
    if (result == 0) {
        printf("✅ 角度限位设置成功：X轴±90°，Y轴±45°\r\n");
    }
    
    // 3. 绝对角度控制示例
    printf("\n=== 绝对角度控制示例 ===\r\n");
    
    // 转到指定绝对位置
    printf("📍 控制云台转到绝对位置：X=30°, Y=-20°\r\n");
    result = Step_Motor_Rotate_To_Angle(30.0f, -20.0f, 40);  // 速度40RPM
    if (result == 0) {
        printf("✅ 绝对角度控制指令发送成功\r\n");
        
        // 等待运动完成
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
            printf("⏳ 云台运动中...\r\n");
        }
        printf("✅ 云台已到达目标位置\r\n");
    } else {
        printf("❌ 绝对角度控制失败，错误码：%d\r\n", result);
    }
    
    // 获取当前角度
    Step_Motor_Get_Current_Angle(&current_x, &current_y);
    printf("📊 当前云台角度：X=%.2f°, Y=%.2f°\r\n", current_x, current_y);
    
    HAL_Delay(2000);  // 停留2秒
    
    // 4. 相对角度控制示例
    printf("\n=== 相对角度控制示例 ===\r\n");
    
    // 相对当前位置移动
    printf("📍 控制云台相对移动：X轴顺时针转15°，Y轴逆时针转10°\r\n");
    result = Step_Motor_Rotate_By_Angle(15.0f, -10.0f, 30);  // 速度30RPM
    if (result == 0) {
        printf("✅ 相对角度控制指令发送成功\r\n");
        
        // 等待运动完成
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
            printf("⏳ 云台运动中...\r\n");
        }
        printf("✅ 云台相对移动完成\r\n");
    } else {
        printf("❌ 相对角度控制失败，错误码：%d\r\n", result);
    }
    
    // 获取当前角度
    Step_Motor_Get_Current_Angle(&current_x, &current_y);
    printf("📊 当前云台角度：X=%.2f°, Y=%.2f°\r\n", current_x, current_y);
    
    HAL_Delay(2000);  // 停留2秒
    
    // 5. 回到零点位置
    printf("\n=== 回到零点位置 ===\r\n");
    printf("📍 控制云台回到零点位置\r\n");
    result = Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 50);  // 速度50RPM
    if (result == 0) {
        printf("✅ 回零指令发送成功\r\n");
        
        // 等待运动完成
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
            printf("⏳ 云台回零中...\r\n");
        }
        printf("✅ 云台已回到零点位置\r\n");
    }
    
    // 获取当前角度
    Step_Motor_Get_Current_Angle(&current_x, &current_y);
    printf("📊 当前云台角度：X=%.2f°, Y=%.2f°\r\n", current_x, current_y);
    
    printf("\n🎉 云台角度控制示例演示完成！\r\n");
}

/**
 * @brief 云台扫描模式示例
 * @note 演示云台进行扫描运动
 */
void Gimbal_Scan_Mode_Example(void)
{
    printf("\n=== 云台扫描模式示例 ===\r\n");
    
    // 水平扫描：左右摆动
    printf("🔄 开始水平扫描模式\r\n");
    for (int i = 0; i < 3; i++) {
        // 向右转45°
        printf("➡️ 向右转45°\r\n");
        Step_Motor_Rotate_To_Angle(45.0f, 0.0f, 60);
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
        }
        HAL_Delay(1000);
        
        // 向左转-45°
        printf("⬅️ 向左转-45°\r\n");
        Step_Motor_Rotate_To_Angle(-45.0f, 0.0f, 60);
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
        }
        HAL_Delay(1000);
    }
    
    // 回到中心位置
    printf("🎯 回到中心位置\r\n");
    Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 60);
    while (Step_Motor_Is_Moving()) {
        HAL_Delay(100);
    }
    
    printf("✅ 水平扫描完成\r\n");
}

/**
 * @brief 云台跟踪模式示例
 * @note 演示云台跟踪目标的运动
 */
void Gimbal_Tracking_Mode_Example(void)
{
    printf("\n=== 云台跟踪模式示例 ===\r\n");
    
    // 模拟跟踪一个移动目标
    float target_positions[][2] = {
        {10.0f, 5.0f},    // 目标位置1
        {25.0f, 15.0f},   // 目标位置2
        {-15.0f, 20.0f},  // 目标位置3
        {-30.0f, -10.0f}, // 目标位置4
        {0.0f, 0.0f}      // 回到中心
    };
    
    int num_positions = sizeof(target_positions) / sizeof(target_positions[0]);
    
    printf("🎯 开始目标跟踪模式，共%d个目标位置\r\n", num_positions);
    
    for (int i = 0; i < num_positions; i++) {
        float target_x = target_positions[i][0];
        float target_y = target_positions[i][1];
        
        printf("📍 跟踪目标%d：X=%.1f°, Y=%.1f°\r\n", i+1, target_x, target_y);
        
        // 快速跟踪到目标位置
        int8_t result = Step_Motor_Rotate_To_Angle(target_x, target_y, 80);
        if (result == 0) {
            // 等待运动完成
            while (Step_Motor_Is_Moving()) {
                HAL_Delay(50);
            }
            printf("✅ 已锁定目标%d\r\n", i+1);
            HAL_Delay(1500);  // 停留1.5秒观察目标
        } else {
            printf("❌ 跟踪目标%d失败\r\n", i+1);
        }
    }
    
    printf("✅ 目标跟踪演示完成\r\n");
}

/**
 * @brief 云台精度测试示例
 * @note 测试云台的角度控制精度
 */
void Gimbal_Precision_Test_Example(void)
{
    printf("\n=== 云台精度测试示例 ===\r\n");
    
    float test_angles[] = {1.0f, 5.0f, 10.0f, 30.0f, 45.0f};
    int num_tests = sizeof(test_angles) / sizeof(test_angles[0]);
    
    printf("🔬 开始精度测试，测试角度：");
    for (int i = 0; i < num_tests; i++) {
        printf("%.1f° ", test_angles[i]);
    }
    printf("\r\n");
    
    // 重置零点
    Step_Motor_Reset_Angle_Zero();
    HAL_Delay(500);
    
    for (int i = 0; i < num_tests; i++) {
        float test_angle = test_angles[i];
        float current_x, current_y;
        
        printf("\n📐 测试角度：%.1f°\r\n", test_angle);
        
        // X轴正向测试
        printf("  ➡️ X轴正向转动%.1f°\r\n", test_angle);
        Step_Motor_Rotate_To_Angle(test_angle, 0.0f, 20);  // 慢速精确控制
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
        }
        
        Step_Motor_Get_Current_Angle(&current_x, &current_y);
        float error_x = current_x - test_angle;
        printf("  📊 目标：%.1f°，实际：%.2f°，误差：%.3f°\r\n", 
               test_angle, current_x, error_x);
        
        HAL_Delay(1000);
        
        // 回到零点
        Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 20);
        while (Step_Motor_Is_Moving()) {
            HAL_Delay(100);
        }
        HAL_Delay(500);
    }
    
    printf("\n✅ 精度测试完成\r\n");
}

/**
 * @brief 在主循环中调用的云台角度控制任务
 * @note 这个函数应该在主循环中周期性调用
 */
void Gimbal_Angle_Control_Task_In_Main_Loop(void)
{
    // 调用云台角度控制任务，用于状态更新
    Step_Motor_Angle_Task();
    
    // 这里可以添加其他周期性任务
    // 例如：检查按键输入，执行相应的云台控制
    
    static uint32_t last_demo_time = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 每30秒执行一次演示（可选）
    if (current_time - last_demo_time > 30000) {
        last_demo_time = current_time;
        
        // 可以在这里调用演示函数
        // Gimbal_Angle_Control_Example();
    }
}

/**
 * @brief 按键控制云台示例
 * @param key_value: 按键值
 */
void Gimbal_Key_Control_Example(uint8_t key_value)
{
    static float step_angle = 5.0f;  // 每次按键移动5°
    
    switch (key_value) {
        case 1:  // 按键1 - X轴正向
            printf("🔄 X轴顺时针转动%.1f°\r\n", step_angle);
            Step_Motor_Rotate_By_Angle(step_angle, 0.0f, 40);
            break;
            
        case 2:  // 按键2 - X轴负向
            printf("🔄 X轴逆时针转动%.1f°\r\n", step_angle);
            Step_Motor_Rotate_By_Angle(-step_angle, 0.0f, 40);
            break;
            
        case 3:  // 按键3 - Y轴正向
            printf("🔄 Y轴顺时针转动%.1f°\r\n", step_angle);
            Step_Motor_Rotate_By_Angle(0.0f, step_angle, 40);
            break;
            
        case 4:  // 按键4 - Y轴负向
            printf("🔄 Y轴逆时针转动%.1f°\r\n", step_angle);
            Step_Motor_Rotate_By_Angle(0.0f, -step_angle, 40);
            break;
            
        case 5:  // 按键5 - 回到零点
            printf("🎯 云台回到零点位置\r\n");
            Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 60);
            break;
            
        case 6:  // 按键6 - 停止运动
            printf("⏹️ 停止云台运动\r\n");
            Step_Motor_Stop_Angle_Move();
            break;
            
        case 7:  // 按键7 - 重置零点
            printf("🔄 重置云台零点\r\n");
            Step_Motor_Reset_Angle_Zero();
            break;
            
        case 8:  // 按键8 - 显示当前角度
            {
                float current_x, current_y;
                Step_Motor_Get_Current_Angle(&current_x, &current_y);
                printf("📊 当前云台角度：X=%.2f°, Y=%.2f°\r\n", current_x, current_y);
            }
            break;
            
        default:
            break;
    }
}