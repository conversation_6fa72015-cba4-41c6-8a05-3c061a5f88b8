#include "my_scheduler.h"



task all_task[]=
{
    {led_task, 5,0},
    {Gray_Task,5,0},
    {Encoder_Task,5,0},
    {PID_Task,5,0},
    {bno080_task,10,0},
    {uart_task,5,0},
    {key_task,10,0},
    {oled_task,10,0},
};

uint8_t task_num;

void all_task_init(void)
{
    task_num = sizeof(all_task)/sizeof(task);

    my_oled_init();
    uart_init();
    my_bno080_init();

    led_init();
    PID_Init();
    Encoder_Init();
    Motor_Init();
//	jy901s_init();
    Gray_Init();
    timer_init();
}

void all_task_run(void)
{
    for(uint8_t i=0; i<task_num; i++)
    {
        uint32_t now_time = HAL_GetTick();
        if(now_time >= all_task[i].last_time + all_task[i].task_time)
        {
            all_task[i].last_time = now_time;
            all_task[i].task_fun();
        }
    }
}
