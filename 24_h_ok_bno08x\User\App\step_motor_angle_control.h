/**
 * @file    step_motor_angle_control.h
 * @brief   EMM_V5步进电机云台角度控制函数头文件
 * <AUTHOR> Team
 * @date    2025-01-26
 */

#ifndef __STEP_MOTOR_ANGLE_CONTROL_H
#define __STEP_MOTOR_ANGLE_CONTROL_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <math.h>
#include <stdbool.h>

/* Exported types ------------------------------------------------------------*/

/* Exported constants --------------------------------------------------------*/

/* Exported macro ------------------------------------------------------------*/

/* Exported functions prototypes ---------------------------------------------*/

/**
 * @brief 云台角度控制系统初始化
 * @note 必须在使用角度控制功能前调用
 * @retval 0: 成功, -1: 失败
 */
int8_t Step_Motor_Angle_Init(void);

/**
 * @brief 控制云台旋转到指定的绝对角度
 * @param x_angle: X轴目标角度 (度)，正值为顺时针，负值为逆时针
 * @param y_angle: Y轴目标角度 (度)，正值为顺时针，负值为逆时针  
 * @param speed: 运动速度 (RPM)，范围 1-100，0表示使用默认速度
 * @retval 0: 成功, -1: 参数错误, -2: 超出限位, -3: 轴正在运动
 * 
 * @example 使用示例：
 *   Step_Motor_Rotate_To_Angle(45.0f, -30.0f, 50);  // X轴转到45°，Y轴转到-30°，速度50RPM
 *   Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 0);      // 回到零点位置，使用默认速度
 */
int8_t Step_Motor_Rotate_To_Angle(float x_angle, float y_angle, uint16_t speed);

/**
 * @brief 控制云台相对当前位置旋转指定角度
 * @param x_delta: X轴相对角度 (度)，正值为顺时针，负值为逆时针
 * @param y_delta: Y轴相对角度 (度)，正值为顺时针，负值为逆时针
 * @param speed: 运动速度 (RPM)，范围 1-100，0表示使用默认速度
 * @retval 0: 成功, -1: 参数错误, -2: 超出限位, -3: 轴正在运动
 * 
 * @example 使用示例：
 *   Step_Motor_Rotate_By_Angle(10.0f, -5.0f, 30);   // X轴顺时针转10°，Y轴逆时针转5°
 *   Step_Motor_Rotate_By_Angle(-90.0f, 0.0f, 0);    // X轴逆时针转90°，Y轴不动
 */
int8_t Step_Motor_Rotate_By_Angle(float x_delta, float y_delta, uint16_t speed);

/**
 * @brief 设置云台角度限位范围
 * @param x_min: X轴最小角度 (度)
 * @param x_max: X轴最大角度 (度)
 * @param y_min: Y轴最小角度 (度)
 * @param y_max: Y轴最大角度 (度)
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Step_Motor_Set_Angle_Limits(float x_min, float x_max, float y_min, float y_max);

/**
 * @brief 获取云台当前角度
 * @param x_angle: 返回X轴当前角度的指针
 * @param y_angle: 返回Y轴当前角度的指针
 * @retval 0: 成功, -1: 参数错误
 */
int8_t Step_Motor_Get_Current_Angle(float *x_angle, float *y_angle);

/**
 * @brief 重置云台角度零点（将当前位置设为零点）
 * @retval 0: 成功
 */
int8_t Step_Motor_Reset_Angle_Zero(void);

/**
 * @brief 检查云台是否正在运动
 * @retval 0: 静止, 1: 运动中
 */
uint8_t Step_Motor_Is_Moving(void);

/**
 * @brief 停止云台运动
 * @retval 0: 成功
 */
int8_t Step_Motor_Stop_Angle_Move(void);

/**
 * @brief 云台角度控制任务（需要周期性调用）
 * @note 建议在10-50ms周期内调用，用于更新运动状态
 */
void Step_Motor_Angle_Task(void);

#ifdef __cplusplus
}
#endif

#endif /* __STEP_MOTOR_ANGLE_CONTROL_H */