# EMM_V5云台角度控制函数使用说明

## 📋 目录
1. [功能概述](#功能概述)
2. [文件说明](#文件说明)
3. [函数接口详解](#函数接口详解)
4. [使用步骤](#使用步骤)
5. [代码示例](#代码示例)
6. [注意事项](#注意事项)
7. [故障排除](#故障排除)

---

## 🎯 功能概述

这套云台角度控制函数为EMM_V5步进电机提供了**高级角度控制接口**，让您可以：

✅ **精确角度控制**：直接指定角度值，无需计算脉冲数  
✅ **绝对/相对定位**：支持绝对角度和相对角度两种控制模式  
✅ **安全限位保护**：自动检查角度限位，防止机械损坏  
✅ **运动状态管理**：实时跟踪运动状态，避免指令冲突  
✅ **简单易用**：封装复杂的EMM_V5通信协议，提供直观的API  

---

## 📁 文件说明

### 核心文件
- **`step_motor_angle_control.h`** - 头文件，包含所有函数声明
- **`step_motor_angle_control.c`** - 实现文件，包含所有函数实现
- **`云台角度控制使用示例.c`** - 使用示例和演示代码

### 依赖文件
- **`Emm_V5.h/.c`** - EMM_V5通信协议（已存在）
- **`step_motor_bsp.h/.c`** - 步进电机基础驱动（已存在）
- **`usart_app.h/.c`** - 串口应用层（已存在）

---

## 🔧 函数接口详解

### 1. 系统初始化

```c
int8_t Step_Motor_Angle_Init(void);
```

**功能**：初始化云台角度控制系统  
**参数**：无  
**返回值**：0=成功，-1=失败  
**说明**：必须在使用其他角度控制函数前调用

---

### 2. 绝对角度控制（核心函数）

```c
int8_t Step_Motor_Rotate_To_Angle(float x_angle, float y_angle, uint16_t speed);
```

**功能**：控制云台转到指定的绝对角度位置  
**参数**：
- `x_angle`：X轴目标角度（度），正值=顺时针，负值=逆时针
- `y_angle`：Y轴目标角度（度），正值=顺时针，负值=逆时针
- `speed`：运动速度（RPM），范围1-100，0=使用默认速度30RPM

**返回值**：
- `0`：成功
- `-1`：参数错误（速度超范围）
- `-2`：超出角度限位
- `-3`：云台正在运动中

**使用示例**：
```c
// 转到绝对位置：X轴45°，Y轴-30°，速度50RPM
Step_Motor_Rotate_To_Angle(45.0f, -30.0f, 50);

// 回到零点位置，使用默认速度
Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 0);
```

---

### 3. 相对角度控制（核心函数）

```c
int8_t Step_Motor_Rotate_By_Angle(float x_delta, float y_delta, uint16_t speed);
```

**功能**：控制云台相对当前位置旋转指定角度  
**参数**：
- `x_delta`：X轴相对角度（度），正值=顺时针，负值=逆时针
- `y_delta`：Y轴相对角度（度），正值=顺时针，负值=逆时针
- `speed`：运动速度（RPM），范围1-100，0=使用默认速度30RPM

**返回值**：同绝对角度控制

**使用示例**：
```c
// 相对当前位置：X轴顺时针转10°，Y轴逆时针转5°
Step_Motor_Rotate_By_Angle(10.0f, -5.0f, 30);

// X轴逆时针转90°，Y轴不动
Step_Motor_Rotate_By_Angle(-90.0f, 0.0f, 0);
```

---

### 4. 辅助功能函数

#### 设置角度限位
```c
int8_t Step_Motor_Set_Angle_Limits(float x_min, float x_max, float y_min, float y_max);
```

**功能**：设置云台的角度运动范围  
**默认限位**：X轴和Y轴都是±50°

#### 获取当前角度
```c
int8_t Step_Motor_Get_Current_Angle(float *x_angle, float *y_angle);
```

**功能**：获取云台当前的角度位置

#### 重置零点
```c
int8_t Step_Motor_Reset_Angle_Zero(void);
```

**功能**：将当前位置设为新的零点

#### 检查运动状态
```c
uint8_t Step_Motor_Is_Moving(void);
```

**功能**：检查云台是否正在运动  
**返回值**：0=静止，1=运动中

#### 停止运动
```c
int8_t Step_Motor_Stop_Angle_Move(void);
```

**功能**：立即停止云台运动

#### 状态更新任务
```c
void Step_Motor_Angle_Task(void);
```

**功能**：云台状态更新任务，需要在主循环中周期性调用（建议10-50ms）

---

## 🚀 使用步骤

### 第1步：添加文件到工程
1. 将 `step_motor_angle_control.h` 和 `step_motor_angle_control.c` 添加到您的STM32工程
2. 在需要使用的文件中包含头文件：
   ```c
   #include "step_motor_angle_control.h"
   ```

### 第2步：系统初始化
在主函数中初始化系统：
```c
int main(void)
{
    // ... 其他初始化代码 ...
    
    // 初始化云台角度控制系统
    if (Step_Motor_Angle_Init() == 0) {
        printf("云台角度控制系统初始化成功\r\n");
    } else {
        printf("云台角度控制系统初始化失败\r\n");
    }
    
    // ... 进入主循环 ...
}
```

### 第3步：在主循环中添加任务
```c
while (1)
{
    // 云台角度控制任务（必须周期性调用）
    Step_Motor_Angle_Task();
    
    // 您的其他任务...
    
    HAL_Delay(10);  // 10ms周期
}
```

### 第4步：使用角度控制功能
```c
// 示例：控制云台转到指定角度
if (Step_Motor_Rotate_To_Angle(30.0f, -15.0f, 40) == 0) {
    printf("角度控制指令发送成功\r\n");
    
    // 等待运动完成
    while (Step_Motor_Is_Moving()) {
        HAL_Delay(100);
        printf("云台运动中...\r\n");
    }
    printf("云台已到达目标位置\r\n");
}
```

---

## 💡 代码示例

### 基础使用示例
```c
void Basic_Gimbal_Control_Example(void)
{
    // 1. 初始化
    Step_Motor_Angle_Init();
    
    // 2. 转到指定绝对位置
    Step_Motor_Rotate_To_Angle(45.0f, -30.0f, 50);
    
    // 3. 等待运动完成
    while (Step_Motor_Is_Moving()) {
        HAL_Delay(100);
    }
    
    // 4. 相对移动
    Step_Motor_Rotate_By_Angle(15.0f, 10.0f, 30);
    
    // 5. 等待运动完成
    while (Step_Motor_Is_Moving()) {
        HAL_Delay(100);
    }
    
    // 6. 回到零点
    Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 60);
}
```

### 按键控制示例
```c
void Key_Control_Gimbal(uint8_t key)
{
    float step = 5.0f;  // 每次移动5°
    
    switch (key) {
        case 1:  // 上
            Step_Motor_Rotate_By_Angle(0.0f, step, 40);
            break;
        case 2:  // 下
            Step_Motor_Rotate_By_Angle(0.0f, -step, 40);
            break;
        case 3:  // 左
            Step_Motor_Rotate_By_Angle(-step, 0.0f, 40);
            break;
        case 4:  // 右
            Step_Motor_Rotate_By_Angle(step, 0.0f, 40);
            break;
        case 5:  // 回中心
            Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 60);
            break;
    }
}
```

### 扫描模式示例
```c
void Gimbal_Scan_Mode(void)
{
    // 水平扫描：左右摆动
    for (int i = 0; i < 5; i++) {
        // 向右
        Step_Motor_Rotate_To_Angle(45.0f, 0.0f, 60);
        while (Step_Motor_Is_Moving()) HAL_Delay(100);
        HAL_Delay(1000);
        
        // 向左
        Step_Motor_Rotate_To_Angle(-45.0f, 0.0f, 60);
        while (Step_Motor_Is_Moving()) HAL_Delay(100);
        HAL_Delay(1000);
    }
    
    // 回中心
    Step_Motor_Rotate_To_Angle(0.0f, 0.0f, 60);
}
```

---

## ⚠️ 注意事项

### 1. 角度系统说明
- **角度单位**：度（°）
- **角度方向**：正值=顺时针，负值=逆时针
- **角度精度**：约0.1125°（基于200步/圈，16细分）
- **角度范围**：默认±50°，可通过 `Step_Motor_Set_Angle_Limits()` 修改

### 2. 速度参数说明
- **速度单位**：RPM（转/分钟）
- **速度范围**：1-100 RPM
- **默认速度**：30 RPM
- **建议速度**：
  - 精确定位：10-30 RPM
  - 正常运动：30-60 RPM
  - 快速运动：60-100 RPM

### 3. 安全限位
- 系统会自动检查角度限位，超出范围的指令会被拒绝
- 默认限位为±50°，建议根据实际机械结构调整
- 可通过 `Step_Motor_Set_Angle_Limits()` 设置自定义限位

### 4. 运动状态管理
- 同一时间只能执行一个运动指令
- 新指令会被拒绝，直到当前运动完成
- 使用 `Step_Motor_Is_Moving()` 检查运动状态
- 使用 `Step_Motor_Stop_Angle_Move()` 紧急停止

### 5. 任务调用要求
- **必须**在主循环中周期性调用 `Step_Motor_Angle_Task()`
- 建议调用周期：10-50ms
- 该任务用于更新运动状态和超时保护

---

## 🔧 故障排除

### 问题1：云台不响应角度控制指令
**可能原因**：
- 未调用 `Step_Motor_Angle_Init()` 初始化
- EMM_V5通信故障
- 串口连接问题

**解决方法**：
1. 确保已调用初始化函数
2. 检查串口连接和波特率
3. 使用示例代码测试基础功能

### 问题2：角度控制不精确
**可能原因**：
- 步进电机参数配置错误
- 机械间隙或松动
- 电机细分设置不匹配

**解决方法**：
1. 检查 `MOTOR_STEPS_PER_REV` 和 `MOTOR_MICROSTEPS` 参数
2. 检查机械连接是否牢固
3. 确认EMM_V5的细分设置

### 问题3：云台运动卡顿或抖动
**可能原因**：
- 速度设置过高
- 加速度设置不当
- 电源供电不足

**解决方法**：
1. 降低运动速度（使用20-40 RPM）
2. 检查电源供电能力
3. 调整 `DEFAULT_ANGLE_ACCEL` 参数

### 问题4：角度限位不生效
**可能原因**：
- 限位参数设置错误
- 当前角度记录不准确

**解决方法**：
1. 使用 `Step_Motor_Reset_Angle_Zero()` 重置零点
2. 检查限位参数设置
3. 重新初始化系统

### 问题5：运动状态异常
**可能原因**：
- 未在主循环中调用 `Step_Motor_Angle_Task()`
- 任务调用周期过长

**解决方法**：
1. 确保在主循环中周期性调用任务函数
2. 调整任务调用周期到10-50ms
3. 检查是否有阻塞性代码影响任务执行

---

## 📊 技术参数

| 参数 | 数值 | 说明 |
|------|------|------|
| 角度分辨率 | 0.1125° | 基于200步/圈，16细分 |
| 最大角度范围 | ±180° | 单次移动限制 |
| 默认限位 | ±50° | 可自定义设置 |
| 速度范围 | 1-100 RPM | 可调节运动速度 |
| 默认速度 | 30 RPM | 平衡精度和速度 |
| 角度容差 | 0.1° | 小于此值不执行运动 |
| 超时保护 | 5秒 | 运动超时自动重置状态 |

---

## 🎉 总结

这套云台角度控制函数提供了**简单易用**且**功能强大**的角度控制接口，让您可以：

✅ **专注业务逻辑**：无需关心底层通信协议和脉冲计算  
✅ **安全可靠**：内置限位保护和状态管理  
✅ **灵活控制**：支持绝对和相对两种控制模式  
✅ **易于集成**：标准化接口，便于集成到现有项目  

通过这套函数，您可以轻松实现**云台控制**、**目标跟踪**、**扫描模式**等各种应用场景！